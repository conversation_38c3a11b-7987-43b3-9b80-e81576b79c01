/* نظام حكيم لإدارة العيادات - الأنماط الأساسية */

/* استيراد الخطوط العربية المحسنة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', 'Tajawal', 'Almarai', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    direction: rtl;
    text-align: right;
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.8;
    font-weight: 400;
}

/* تطبيق الخط العربي على جميع العناصر */
*, *::before, *::after {
    font-family: 'Cairo', 'IBM Plex Sans Arabic', 'Tajawal', 'Almarai', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* الألوان الأساسية المحسنة */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --secondary-light: #9f7aea;
    --success-color: #48bb78;
    --danger-color: #f56565;
    --warning-color: #ed8936;
    --info-color: #4299e1;
    --light-color: #f7fafc;
    --dark-color: #2d3748;
    --white: #ffffff;
    --border-color: #e2e8f0;
    --text-color: #2d3748;
    --text-light: #718096;
    --shadow: 0 4px 20px rgba(0,0,0,0.08);
    --shadow-lg: 0 10px 40px rgba(0,0,0,0.15);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* الحاويات */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

[class*="col-"] {
    padding: 0 15px;
}

/* الأزرار مع أنيميشن متقدم */
.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    text-decoration: none;
    font-size: 15px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 120px;
    position: relative;
    overflow: hidden;
    font-family: 'Cairo', sans-serif !important;
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e879f9 0%, #ef4444 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(240, 147, 251, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    color: white;
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(74, 222, 128, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
    border: none;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(251, 191, 36, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
    color: white;
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(248, 113, 113, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
    color: white;
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(96, 165, 250, 0.4);
}

.btn-light {
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-light:hover {
    background: rgba(255, 255, 255, 1);
    color: #1f2937;
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(255, 255, 255, 0.3);
}

.btn-dark {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: white;
    border: none;
}

.btn-dark:hover {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(55, 65, 81, 0.4);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: #212529;
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

.btn-light {
    background-color: var(--light-color);
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    min-width: 80px;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 16px;
    min-width: 120px;
}

/* البطاقات */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px 20px;
    font-weight: bold;
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    padding: 20px;
}

.card-footer {
    background-color: var(--light-color);
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background-color: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 5px;
}

.valid-feedback {
    color: var(--success-color);
    font-size: 12px;
    margin-top: 5px;
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-child(odd) {
    background-color: rgba(0,0,0,0.02);
}

/* التنبيهات */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: var(--success-color);
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: var(--danger-color);
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: var(--warning-color);
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: var(--info-color);
    color: #0c5460;
}

/* الشريط العلوي */
.navbar {
    background-color: var(--primary-color);
    color: white;
    padding: 15px 0;
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-size: 24px;
    font-weight: bold;
    color: white;
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 10px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
}

/* الشريط الجانبي */
.sidebar {
    background-color: white;
    min-height: 100vh;
    box-shadow: var(--shadow);
    padding: 20px 0;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: block;
    padding: 12px 20px;
    color: var(--dark-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background-color: var(--light-color);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
}

.sidebar-menu i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 20px;
    min-height: 100vh;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 20px;
}

.stats-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
}

.stats-label {
    font-size: 14px;
    opacity: 0.9;
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* التجاوب */
@media (max-width: 768px) {
    .row {
        margin: 0;
    }
    
    [class*="col-"] {
        padding: 0 10px;
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .btn {
        width: 100%;
        margin: 5px 0;
    }
    
    .table {
        font-size: 12px;
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar.active {
        right: 0;
    }
    
    .main-content {
        padding: 10px;
    }
}

/* التذييل المحسن */
.footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.05)"><path d="M0,0 Q250,50 500,25 T1000,0 L1000,100 L0,100 Z"/></svg>');
    background-size: cover;
    animation: wave 8s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-50px); }
}

.footer .social-links a {
    transition: all 0.3s ease;
}

.footer .social-links a:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}

.footer .contact-info div {
    transition: all 0.3s ease;
}

.footer .contact-info div:hover {
    transform: translateX(-5px);
    color: #fff !important;
}

/* إصلاح Bootstrap RTL */
[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .text-md-end {
    text-align: right !important;
}

[dir="rtl"] .text-md-start {
    text-align: left !important;
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }
.mt-3 { margin-top: 1rem; }
.mb-3 { margin-bottom: 1rem; }
.p-3 { padding: 1rem; }
.rounded { border-radius: 0.375rem; }
.shadow { box-shadow: var(--shadow); }
