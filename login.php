<?php
/**
 * صفحة تسجيل الدخول - نظام حكيم لإدارة العيادات
 */
require_once 'config/config.php';
require_once 'includes/auth.php';
require_once 'includes/language.php';

// التحقق من تسجيل الدخول المسبق
if (isLoggedIn()) {
    redirectByRole();
}

$error_message = '';
$redirect_url = isset($_GET['redirect']) ? $_GET['redirect'] : '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $login_result = login($username, $password);
        
        if ($login_result['success']) {
            // إعادة التوجيه بعد تسجيل الدخول الناجح
            if (!empty($redirect_url)) {
                header('Location: ' . $redirect_url);
            } else {
                redirectByRole();
            }
            exit;
        } else {
            $error_message = $login_result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo getTextDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('login'); ?> - <?php echo t('site_name'); ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/arabic-fonts.css">
    <link rel="stylesheet" href="assets/css/enhanced-colors.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
            font-family: 'Cairo', 'IBM Plex Sans Arabic', sans-serif !important;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="grad1" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" /><stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:1" /></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23grad1)"/><circle cx="800" cy="300" r="150" fill="url(%23grad1)"/><circle cx="300" cy="700" r="120" fill="url(%23grad1)"/><circle cx="700" cy="800" r="80" fill="url(%23grad1)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            33% { transform: translateY(-20px) rotate(1deg) scale(1.05); }
            66% { transform: translateY(10px) rotate(-1deg) scale(0.95); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(30px);
            border-radius: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            max-width: 1100px;
            width: 100%;
            margin: 20px;
            position: relative;
            z-index: 2;
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><path d="M0,0 Q250,50 500,25 T1000,0 L1000,100 L0,100 Z"/></svg>');
            background-size: cover;
            animation: wave 8s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-50px); }
        }
        
        .login-logo {
            font-size: 64px;
            margin-bottom: 25px;
            animation: pulse 2s ease-in-out infinite;
            position: relative;
            z-index: 2;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .login-title {
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
            font-family: 'Cairo', sans-serif !important;
        }

        .login-subtitle {
            opacity: 0.95;
            font-size: 18px;
            font-weight: 300;
            position: relative;
            z-index: 2;
            font-family: 'Cairo', sans-serif !important;
        }
        
        .login-body {
            padding: 50px;
            background: rgba(255, 255, 255, 0.1);
        }

        .login-form {
            max-width: 450px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
        }

        .form-control {
            padding: 18px 20px;
            font-size: 16px;
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif !important;
            width: 100%;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-family: 'Cairo', sans-serif !important;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-login {
            width: 100%;
            padding: 18px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif !important;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        
        .login-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .login-links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 10px;
        }
        
        .login-links a:hover {
            text-decoration: underline;
        }
        
        .demo-accounts {
            background: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .demo-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-color);
        }
        
        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .demo-account:hover {
            background-color: #f0f0f0;
        }
        
        .demo-info {
            flex: 1;
        }
        
        .demo-role {
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .demo-credentials {
            font-size: 12px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 10px;
            }
            
            .login-header,
            .login-body {
                padding: 20px;
            }
            
            .login-logo {
                font-size: 36px;
            }
            
            .login-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- رأس تسجيل الدخول -->
        <div class="login-header">
            <!-- مبدل اللغة -->
            <div style="position: absolute; top: 20px; <?php echo $current_language === 'ar' ? 'left' : 'right'; ?>: 20px; z-index: 10;">
                <a href="<?php echo getLanguageSwitchUrl('ar'); ?>"
                   class="btn btn-sm <?php echo $current_language === 'ar' ? 'btn-warning' : 'btn-light'; ?>"
                   style="margin: 2px; font-size: 12px; padding: 8px 12px;">عربي</a>
                <a href="<?php echo getLanguageSwitchUrl('en'); ?>"
                   class="btn btn-sm <?php echo $current_language === 'en' ? 'btn-warning' : 'btn-light'; ?>"
                   style="margin: 2px; font-size: 12px; padding: 8px 12px;">English</a>
            </div>

            <div class="login-logo">
                <i class="fas fa-stethoscope"></i>
            </div>
            <h1 class="login-title"><?php echo t('welcome_to_hakim'); ?></h1>
            <p class="login-subtitle"><?php echo t('smart_clinic_system'); ?></p>
        </div>
        
        <!-- جسم تسجيل الدخول -->
        <div class="login-body">
            <form class="login-form" method="POST" data-validate="true">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i> <?php echo t('username'); ?>
                    </label>
                    <input type="text" name="username" class="form-control"
                           placeholder="<?php echo t('enter_username'); ?>" required
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock"></i> <?php echo t('password'); ?>
                    </label>
                    <input type="password" name="password" class="form-control"
                           placeholder="<?php echo t('enter_password'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label class="form-control" style="display: flex; align-items: center; cursor: pointer; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 12px;">
                        <input type="checkbox" name="remember" style="margin-<?php echo $current_language === 'ar' ? 'left' : 'right'; ?>: 10px;">
                        <?php echo t('remember_me'); ?>
                    </label>
                </div>
                
                <?php if (!empty($redirect_url)): ?>
                    <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($redirect_url); ?>">
                <?php endif; ?>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt"></i> <?php echo t('login'); ?>
                </button>
            </form>
            
            <!-- روابط إضافية -->
            <div class="login-links">
                <a href="forgot-password.php">
                    <i class="fas fa-key"></i> <?php echo t('forgot_password'); ?>
                </a>
                |
                <a href="register.php">
                    <i class="fas fa-user-plus"></i> <?php echo t('create_account'); ?>
                </a>
                |
                <a href="index.php">
                    <i class="fas fa-home"></i> <?php echo t('return_home'); ?>
                </a>
            </div>
            
            <!-- حسابات تجريبية -->
            <div class="demo-accounts">
                <div class="demo-title">
                    <i class="fas fa-flask"></i> <?php echo t('demo_accounts_testing'); ?>
                </div>

                <div class="demo-account" onclick="fillLoginForm('admin', 'password')">
                    <div class="demo-info">
                        <div class="demo-role"><?php echo t('clinic_manager'); ?></div>
                        <div class="demo-credentials">admin / password</div>
                    </div>
                    <i class="fas fa-user-shield" style="color: var(--primary-color);"></i>
                </div>

                <div class="demo-account" onclick="fillLoginForm('doctor1', 'password')">
                    <div class="demo-info">
                        <div class="demo-role"><?php echo t('doctor'); ?></div>
                        <div class="demo-credentials">doctor1 / password</div>
                    </div>
                    <i class="fas fa-user-md" style="color: var(--success-color);"></i>
                </div>

                <div class="demo-account" onclick="fillLoginForm('secretary1', 'password')">
                    <div class="demo-info">
                        <div class="demo-role"><?php echo t('secretary'); ?></div>
                        <div class="demo-credentials">secretary1 / password</div>
                    </div>
                    <i class="fas fa-user-tie" style="color: var(--info-color);"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script>
        // ملء نموذج تسجيل الدخول بالبيانات التجريبية
        function fillLoginForm(username, password) {
            document.querySelector('input[name="username"]').value = username;
            document.querySelector('input[name="password"]').value = password;
            
            // تأثير بصري
            const form = document.querySelector('.login-form');
            form.style.transform = 'scale(1.02)';
            setTimeout(() => {
                form.style.transform = 'scale(1)';
            }, 200);
        }
        
        // تأثيرات التحميل
        document.querySelector('.login-form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<div class="loading"></div> <?php echo t('login_loading'); ?>';
            submitBtn.disabled = true;
            
            // إعادة تفعيل الزر في حالة الخطأ
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 5000);
        });
        
        // تأثير الظهور التدريجي
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            container.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            
            setTimeout(() => {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
