<?php
/**
 * الصفحة الرئيسية - نظام حكيم لإدارة العيادات
 */
require_once 'config/config.php';
require_once 'includes/language.php';
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo getTextDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('site_name'); ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/arabic-fonts.css">
    <link rel="stylesheet" href="assets/css/enhanced-colors.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* أنماط خاصة بالصفحة الرئيسية */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            color: white;
            padding: 120px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 56px;
            font-weight: 800;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 24px;
            margin-bottom: 50px;
            opacity: 0.95;
            font-weight: 300;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .features-section {
            padding: 100px 0;
            background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }

        .features-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white"><polygon points="0,0 1000,100 1000,0"/></svg>');
            background-size: cover;
        }

        .feature-card {
            text-align: center;
            padding: 50px 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            transition: all 0.4s ease;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 64px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            transition: transform 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .feature-title {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .feature-description {
            color: #666;
            line-height: 1.8;
            font-size: 16px;
        }
        
        .pricing-section {
            padding: 100px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }

        .pricing-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .pricing-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 50px 40px;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            transition: all 0.4s ease;
            border: 1px solid rgba(255,255,255,0.3);
            color: var(--dark-color);
        }

        .pricing-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .pricing-card.featured {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.08);
            border: 3px solid rgba(255,255,255,0.3);
        }

        .pricing-card.featured:hover {
            transform: scale(1.12) translateY(-10px);
        }
        
        .pricing-badge {
            position: absolute;
            top: -15px;
            right: 50%;
            transform: translateX(50%);
            background: var(--primary-color);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .pricing-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--dark-color);
        }
        
        .pricing-price {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .pricing-features {
            list-style: none;
            padding: 0;
            margin: 30px 0;
        }
        
        .pricing-features li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .pricing-features li:last-child {
            border-bottom: none;
        }
        
        .pricing-features i {
            color: var(--success-color);
            margin-left: 10px;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white"><polygon points="0,0 1000,100 0,100"/></svg>');
            background-size: cover;
        }

        .cta-content {
            position: relative;
            z-index: 2;
        }

        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 60px 0 30px;
            text-align: center;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            bottom: 10%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* أنيميشن للبطاقات */
        .feature-card, .pricing-card {
            animation: slideInUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }
        .feature-card:nth-child(5) { animation-delay: 0.5s; }
        .feature-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* أنيميشن للنصوص */
        .hero-title, .hero-subtitle {
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            animation-delay: 0.3s;
            animation-fill-mode: both;
        }

        /* أنيميشن للأزرار */
        .btn {
            animation: bounceIn 0.8s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-6">
                    <a href="index.php" class="navbar-brand">
                        <i class="fas fa-stethoscope"></i> حكيم
                    </a>
                </div>
                <div class="col-6" style="text-align: <?php echo $current_language === 'ar' ? 'left' : 'right'; ?>;">
                    <!-- مبدل اللغة -->
                    <div style="display: inline-block; margin-left: 15px;">
                        <a href="<?php echo getLanguageSwitchUrl('ar'); ?>"
                           class="btn btn-sm <?php echo $current_language === 'ar' ? 'btn-warning' : 'btn-light'; ?>"
                           style="margin: 2px;">عربي</a>
                        <a href="<?php echo getLanguageSwitchUrl('en'); ?>"
                           class="btn btn-sm <?php echo $current_language === 'en' ? 'btn-warning' : 'btn-light'; ?>"
                           style="margin: 2px;">English</a>
                    </div>
                    <a href="login.php" class="btn btn-light"><?php echo t('login'); ?></a>
                    <a href="register.php" class="btn btn-warning"><?php echo t('start_free_trial_now'); ?></a>
                </div>
            </div>
        </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="hero-section">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">🏥 <?php echo t('hero_title'); ?></h1>
                <p class="hero-subtitle"><?php echo t('hero_subtitle'); ?></p>
                <div style="margin-top: 40px;">
                    <a href="register.php" class="btn btn-warning btn-lg" style="margin: 10px; padding: 15px 30px; font-size: 18px; border-radius: 50px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">
                        🚀 <?php echo t('start_free_trial'); ?>
                    </a>
                    <a href="#features" class="btn btn-light btn-lg" style="margin: 10px; padding: 15px 30px; font-size: 18px; border-radius: 50px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">
                        ✨ <?php echo t('discover_features'); ?>
                    </a>
                </div>
                <div style="margin-top: 30px; font-size: 16px; opacity: 0.9;">
                    ⭐ <?php echo t('free_trial_desc'); ?> • 🔒 <?php echo t('free_support'); ?> • 📱 <?php echo t('comprehensive_reports'); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المزايا -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 style="font-size: 42px; color: var(--dark-color); margin-bottom: 25px; font-weight: 800;">
                    🌟 <?php echo t('why_choose_hakim'); ?>
                </h2>
                <p style="font-size: 20px; color: #666; max-width: 600px; margin: 0 auto; line-height: 1.8;">
                    <?php echo t('integrated_system'); ?>
                </p>
            </div>
            
            <div class="row">
                <div class="col-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">👥 <?php echo t('patient_management'); ?></h3>
                        <p class="feature-description">
                            <?php echo t('patient_management_desc'); ?>
                        </p>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3 class="feature-title">📅 <?php echo t('smart_appointments'); ?></h3>
                        <p class="feature-description">
                            <?php echo t('smart_appointments_desc'); ?>
                        </p>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-prescription-bottle-alt"></i>
                        </div>
                        <h3 class="feature-title">💊 <?php echo t('electronic_prescriptions'); ?></h3>
                        <p class="feature-description">
                            <?php echo t('electronic_prescriptions_desc'); ?>
                        </p>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <h3 class="feature-title">💰 <?php echo t('billing_system'); ?></h3>
                        <p class="feature-description">
                            <?php echo t('billing_system_desc'); ?>
                        </p>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">📊 <?php echo t('reports_analytics'); ?></h3>
                        <p class="feature-description">
                            <?php echo t('reports_analytics_desc'); ?>
                        </p>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">📱 <?php echo t('responsive_design'); ?></h3>
                        <p class="feature-description">
                            <?php echo t('responsive_design_desc'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الأسعار -->
    <section id="pricing" class="pricing-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 style="font-size: 42px; color: white; margin-bottom: 25px; font-weight: 800; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    💎 <?php echo t('subscription_plans'); ?>
                </h2>
                <p style="font-size: 20px; color: rgba(255,255,255,0.9); max-width: 600px; margin: 0 auto;">
                    <?php echo t('choose_plan'); ?>
                </p>
            </div>
            
            <div class="row">
                <div class="col-4">
                    <div class="pricing-card">
                        <h3 class="pricing-title"><?php echo t('basic_plan'); ?></h3>
                        <div class="pricing-price">
                            <?php echo $current_language === 'ar' ? '99 ريال' : '99 SAR'; ?>
                            <span style="font-size: 16px;"><?php echo $current_language === 'ar' ? '/شهر' : '/month'; ?></span>
                        </div>
                        <ul class="pricing-features">
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? 'حتى 100 مريض' : 'Up to 100 patients'; ?></li>
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? 'طبيبان' : '2 doctors'; ?></li>
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? 'إدارة المواعيد' : 'Appointment management'; ?></li>
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? 'الوصفات الطبية' : 'Medical prescriptions'; ?></li>
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? 'الفواتير الأساسية' : 'Basic invoicing'; ?></li>
                        </ul>
                        <a href="register.php?plan=basic" class="btn btn-primary"><?php echo t('choose_this_plan'); ?></a>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="pricing-card featured">
                        <div class="pricing-badge"><?php echo t('most_popular'); ?></div>
                        <h3 class="pricing-title"><?php echo t('pro_plan'); ?></h3>
                        <div class="pricing-price">
                            <?php echo $current_language === 'ar' ? '199 ريال' : '199 SAR'; ?>
                            <span style="font-size: 16px;"><?php echo $current_language === 'ar' ? '/شهر' : '/month'; ?></span>
                        </div>
                        <ul class="pricing-features">
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? 'حتى 500 مريض' : 'Up to 500 patients'; ?></li>
                            <li><i class="fas fa-check"></i> <?php echo $current_language === 'ar' ? '5 أطباء' : '5 doctors'; ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('all_basic_features'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('advanced_reports'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('automatic_notifications'); ?></li>
                            <li><i class="fas fa-check"></i> <?php echo t('multiple_branches'); ?></li>
                        </ul>
                        <a href="register.php?plan=pro" class="btn btn-primary"><?php echo t('choose_this_plan'); ?></a>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="pricing-card">
                        <h3 class="pricing-title">خطة المؤسسات</h3>
                        <div class="pricing-price">399 ريال<span style="font-size: 16px;">/شهر</span></div>
                        <ul class="pricing-features">
                            <li><i class="fas fa-check"></i> مرضى غير محدود</li>
                            <li><i class="fas fa-check"></i> أطباء غير محدود</li>
                            <li><i class="fas fa-check"></i> جميع المزايا</li>
                            <li><i class="fas fa-check"></i> دعم فني مخصص</li>
                            <li><i class="fas fa-check"></i> تخصيص كامل</li>
                            <li><i class="fas fa-check"></i> تدريب مجاني</li>
                        </ul>
                        <a href="register.php?plan=enterprise" class="btn btn-primary">اختر هذه الخطة</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الدعوة للعمل -->
    <section class="cta-section">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="cta-content">
                <h2 style="font-size: 48px; margin-bottom: 25px; font-weight: 800; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    🚀 <?php echo t('start_journey'); ?>
                </h2>
                <p style="font-size: 22px; margin-bottom: 40px; opacity: 0.95;">
                    <?php echo t('free_trial_desc'); ?>
                </p>
                <div style="margin-bottom: 30px;">
                    <a href="register.php" class="btn btn-warning btn-lg" style="padding: 18px 40px; font-size: 20px; border-radius: 50px; box-shadow: 0 8px 20px rgba(0,0,0,0.2); margin: 10px;">
                        ✨ <?php echo t('start_free_trial_now'); ?>
                    </a>
                </div>
                <div style="font-size: 16px; opacity: 0.9;">
                    🎯 <?php echo t('quick_setup'); ?> • 🔧 <?php echo t('free_support'); ?> • 📊 <?php echo t('comprehensive_reports'); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div style="margin-bottom: 30px;">
                <h3 style="font-size: 28px; margin-bottom: 15px; color: white;">
                    <i class="fas fa-stethoscope"></i> حكيم
                </h3>
                <p style="font-size: 16px; opacity: 0.9; max-width: 500px; margin: 0 auto;">
                    <?php echo t('most_advanced_system'); ?>
                </p>
            </div>

            <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 20px;">
                <p style="margin-bottom: 10px;">&copy; 2024 <?php echo t('site_name'); ?>. <?php echo t('all_rights_reserved'); ?></p>
                <p style="opacity: 0.8;"><?php echo t('developed_by'); ?> 💻 | <?php echo t('made_with_love'); ?></p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script>
        // تأثيرات التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تأثير الظهور عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // مراقبة البطاقات
        document.querySelectorAll('.feature-card, .pricing-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // تأثيرات إضافية للأزرار
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px) scale(1.05)';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>

    <script src="assets/js/main.js"></script>
</body>
</html>
